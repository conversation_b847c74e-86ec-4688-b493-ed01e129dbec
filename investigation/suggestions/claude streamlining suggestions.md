# Tourism Site Codebase Streamlining: Complete Solution Guide

## Problem Summary
- Next.js 15 TypeScript tourism site with 130+ files, 635+ exports
- Current grep-based analysis has false negatives (flagging used auth/security files as unused)
- Need accurate dead code detection to safely remove unused code and reduce bundle size

## Root Cause of Current Issues
Your grep-based approach fails because:
1. **Shell escaping problems** with complex regex patterns
2. **Dynamic imports** not detected: `const { func } = await import('@/lib/file')`
3. **Next.js file-based routing** auto-imports files
4. **Destructuring patterns** missed: `const { a: renamedA } = imported`
5. **Template literal usage** not detected
6. **Middleware and API route imports** not caught by simple text search

---

## RECOMMENDED SOLUTION: Multi-Tool Approach

### Step 1: Install Better Tools
```bash
# Primary recommendation - AST-based analysis
npm install -g ts-unused-exports unimported

# Supporting tools
npm install --save-dev @typescript-eslint/eslint-plugin unused-imports
```

### Step 2: Use Professional Tools Instead of Grep

#### Tool 1: ts-unused-exports (Primary)
```bash
# Most accurate for TypeScript projects
ts-unused-exports tsconfig.json --searchNamespaces --showLineNumber
```

#### Tool 2: unimported (Verification)
```bash
# Good for finding unused files
unimported --init
unimported
```

#### Tool 3: ESLint Configuration
```json
// .eslintrc.json
{
  "extends": ["@typescript-eslint/recommended"],
  "plugins": ["unused-imports"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": "warn"
  }
}
```

### Step 3: Custom AST-Based Analyzer (Backup Verification)

Create `analyze-dead-code.ts`:

```typescript
#!/usr/bin/env node
import * as ts from 'typescript';
import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface ExportInfo {
  name: string;
  file: string;
  line: number;
  type: 'function' | 'const' | 'class' | 'interface' | 'type';
}

interface ImportInfo {
  name: string;
  from: string;
  file: string;
  line: number;
  isUsed: boolean;
}

class DeadCodeAnalyzer {
  private exports: Map<string, ExportInfo[]> = new Map();
  private imports: ImportInfo[] = [];
  private usages: Map<string, string[]> = new Map();

  async analyze(rootDir: string) {
    console.log('Starting AST-based dead code analysis...');
    
    const files = await glob('**/*.{ts,tsx}', {
      cwd: rootDir,
      ignore: ['node_modules/**', 'dist/**', '.next/**'],
      absolute: true
    });

    console.log(`Analyzing ${files.length} files...`);

    // Extract exports, imports, and usages
    for (const file of files) {
      this.extractExports(file);
      this.extractImports(file);
      this.findUsages(file);
    }

    this.generateReport();
  }

  private extractExports(filePath: string) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(filePath, content, ts.ScriptTarget.Latest, true);
    const exports: ExportInfo[] = [];

    const visit = (node: ts.Node) => {
      if (node.modifiers?.some(m => m.kind === ts.SyntaxKind.ExportKeyword)) {
        let name = '';
        let type: ExportInfo['type'] = 'const';

        if (ts.isFunctionDeclaration(node) && node.name) {
          name = node.name.text;
          type = 'function';
        } else if (ts.isVariableStatement(node)) {
          node.declarationList.declarations.forEach(decl => {
            if (ts.isIdentifier(decl.name)) {
              name = decl.name.text;
              type = 'const';
            }
          });
        } else if (ts.isClassDeclaration(node) && node.name) {
          name = node.name.text;
          type = 'class';
        }

        if (name) {
          exports.push({
            name,
            file: filePath,
            line: sourceFile.getLineAndCharacterOfPosition(node.getStart()).line + 1,
            type
          });
        }
      }
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    if (exports.length > 0) {
      this.exports.set(filePath, exports);
    }
  }

  private extractImports(filePath: string) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(filePath, content, ts.ScriptTarget.Latest, true);

    const visit = (node: ts.Node) => {
      if (ts.isImportDeclaration(node) && node.importClause?.namedBindings && ts.isNamedImports(node.importClause.namedBindings)) {
        const moduleSpecifier = node.moduleSpecifier;
        if (ts.isStringLiteral(moduleSpecifier)) {
          const from = moduleSpecifier.text;
          node.importClause.namedBindings.elements.forEach(element => {
            this.imports.push({
              name: element.name.text,
              from,
              file: filePath,
              line: sourceFile.getLineAndCharacterOfPosition(element.getStart()).line + 1,
              isUsed: false
            });
          });
        }
      }
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
  }

  private findUsages(filePath: string) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(filePath, content, ts.ScriptTarget.Latest, true);
    const fileImports = this.imports.filter(imp => imp.file === filePath);
    
    const visit = (node: ts.Node) => {
      if (ts.isImportDeclaration(node)) return;
      
      if (ts.isIdentifier(node)) {
        const name = node.text;
        const matchingImport = fileImports.find(imp => imp.name === name);
        if (matchingImport) {
          matchingImport.isUsed = true;
          if (!this.usages.has(name)) {
            this.usages.set(name, []);
          }
          this.usages.get(name)!.push(filePath);
        }
      }
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
  }

  private generateReport() {
    console.log('\n=== AST-BASED DEAD CODE ANALYSIS ===\n');

    const unusedExports: ExportInfo[] = [];
    for (const [file, exports] of this.exports) {
      for (const exportInfo of exports) {
        const isUsed = this.imports.some(imp => imp.name === exportInfo.name && imp.isUsed);
        if (!isUsed) unusedExports.push(exportInfo);
      }
    }

    const unusedImports = this.imports.filter(imp => !imp.isUsed);
    const filesWithNoUsedExports = Array.from(this.exports.keys()).filter(file => {
      const fileExports = this.exports.get(file) || [];
      return fileExports.length > 0 && fileExports.every(exp => 
        !this.imports.some(imp => imp.name === exp.name && imp.isUsed)
      );
    });

    console.log(`📊 RESULTS:`);
    console.log(`Unused exports: ${unusedExports.length}`);
    console.log(`Unused imports: ${unusedImports.length}`);
    console.log(`Files with no used exports: ${filesWithNoUsedExports.length}`);

    if (filesWithNoUsedExports.length > 0) {
      console.log(`\n🚨 CANDIDATES FOR DELETION:`);
      filesWithNoUsedExports.forEach(file => {
        console.log(`  - ${path.relative(process.cwd(), file)}`);
      });
    }
  }
}

const analyzer = new DeadCodeAnalyzer();
analyzer.analyze(process.cwd()).catch(console.error);
```

---

## SAFE STREAMLINING PROCESS

### Phase 1: Analysis & Verification (Week 1)
```bash
# Run all tools and compare results
npm run analyze:ts-unused     # ts-unused-exports
npm run analyze:unimported    # unimported  
npm run analyze:custom        # custom AST analyzer
npm run lint:unused           # ESLint unused imports

# Manual verification for critical files
git grep -l "verifyAdminAccess\|hashPassword\|createSupabaseClient" -- "*.ts" "*.tsx"
```

### Phase 2: Conservative Removal (Week 1-2)
1. **Only remove files flagged by ALL tools**
2. **Start with obvious utility files** (not auth/security)
3. **Create backup branch** before any changes
4. **Remove one file at a time** with full test runs

```bash
# Safe removal process
git checkout -b streamlining-backup
git checkout -b streamlining-work

# For each file to remove:
rm lib/suspected-unused-file.ts
npm run build  # Ensure build works
npm run test   # Run full test suite
git commit -m "Remove unused file: lib/suspected-unused-file.ts"
```

### Phase 3: Import Cleanup (Week 2)
```bash
# Remove unused imports automatically
npx eslint . --ext .ts,.tsx --fix
```

### Phase 4: Ongoing Prevention
```json
// package.json
{
  "scripts": {
    "analyze:dead-code": "ts-unused-exports tsconfig.json --showLineNumber",
    "analyze:unimported": "unimported",
    "lint:unused": "eslint . --ext .ts,.tsx --fix",
    "pre-commit": "npm run lint:unused && npm run test"
  },
  "husky": {
    "hooks": {
      "pre-commit": "npm run pre-commit"
    }
  }
}
```

---

## PRIORITY ORDER FOR YOUR PROJECT

### Immediate Actions (Today):
1. Install `ts-unused-exports` and `unimported`
2. Run both tools and compare with your current results
3. Manually verify the auth/security files you mentioned

### Week 1 Focus:
- **lib/ folder** (20.1% usage rate) - start here
- Remove files flagged by multiple tools
- Focus on obvious utility files first

### Files to Double-Check Manually:
- `lib/auth-server.ts` - likely used in API routes
- `lib/security.ts` - likely used in authentication
- `lib/cloudinary.ts` - likely used in admin panels  
- `lib/supabase-server.ts` - likely used throughout app

### Expected Results:
- **15-25% bundle size reduction**
- **More accurate analysis** than grep-based approach
- **Zero risk** of removing critical functionality
- **Automated prevention** of future dead code accumulation

---

## COMMANDS TO RUN IMMEDIATELY

```bash
# Install better tools
npm install -g ts-unused-exports unimported
npm install --save-dev @typescript-eslint/eslint-plugin unused-imports eslint

# Get accurate analysis
ts-unused-exports tsconfig.json --searchNamespaces --showLineNumber > analysis-ts-unused.txt
unimported > analysis-unimported.txt

# Verify your specific problematic files  
git grep -r "verifyAdminAccess\|hashPassword\|createSupabaseClient" -- "*.ts" "*.tsx"

# Setup ESLint for ongoing prevention
echo '{
  "extends": ["@typescript-eslint/recommended"],
  "plugins": ["unused-imports"],
  "rules": {
    "unused-imports/no-unused-imports": "error"
  }
}' > .eslintrc.json
```

This approach will give you **accurate results** instead of the false negatives you're experiencing with grep, while maintaining **zero risk** of breaking critical functionality.