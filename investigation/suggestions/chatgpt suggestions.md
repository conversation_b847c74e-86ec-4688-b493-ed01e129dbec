````markdown
# Streamlining Your Tourism Site Codebase

A practical guide for cleaning up, analyzing, and safely removing dead code in a TypeScript/JavaScript codebase—especially after long-term development and potential code noise.

---

## 1. Improved Search Pattern Design

**Why?** Many legacy grep patterns can miss edge cases (like multi-line exports or dynamic imports). Better patterns mean fewer false negatives when identifying unused code.

- **Use AST-aware search tools**
  - **Ripgrep PCRE2 mode** (`rg -P`): Enables advanced regex features like lookarounds and multi-line matching.
    ```bash
    rg -Pzo "export\s+(?:async\s+)?function\s+(\w+)[\s\S]*?}" lib/ | rg -Po "function\s+\K\w+"
    ```
  - **fd + grep**: Combines speed of `fd` with regex power of `grep`, scanning only `.ts`/`.tsx` files for export patterns.
    ```bash
    fd --extension ts --extension tsx --exec grep -Pzo "export\s+(?:const|function|class)\s+\w+" {}
    ```

- **Edge‑case regex patterns**
  - **Dynamic imports**: Useful for catching code-splitting or lazy-loaded logic.
    ```bash
    rg -P "import\(\s*['\"]@/lib/[^'\"]+['\"]\s*\)"
    ```
  - **Re‑exports**: Catches files that re-export other modules (often overlooked by default analysis).
    ```bash
    rg -P "export\s+\*\s+from\s+['\"]@/lib/[^'\"]+['\"]"
    ```
  - **Type-only imports**: These are compile-time only; don’t include them in runtime usage analysis.
    ```bash
    rg -P "import\s+type\s+{[^}]+}\s+from\s+['\"]@/lib/[^'\"]+['\"]"
    ```

---

## 2. Automation Tool Recommendations

**Why?** Manually tracking imports/exports is fragile. These tools automate the detection of unused or stale code.

- **AST-based Dead Code Detectors**
  - **ts-prune**: Detects exported functions/types not used anywhere in the project.
  - **unimported**: Flags files and symbols that are never imported.
  - **depcheck**: Audits unused npm dependencies and unused code patterns.

- **Bundler-integrated Analyzers**
  - **Webpack Bundle Analyzer** / **next-bundle-analyzer**: Helps visualize which modules are included in the final bundle.
  - **esbuild metafile + esbuild-analyze**: Generates JSON reports showing which files are contributing to your build.

- **Custom Analysis with ts-morph**
  - Build scripts that parse the TypeScript AST to identify unused exports and unused imports with fine-grained accuracy.

---

## 3. Methodology Improvements

**Why?** Removing code blindly can introduce regressions. These methods ensure safe, deliberate cleanup.

1. **Combine Static + Dynamic Analysis**
   - *Static*: Tools like `ts-prune` and `depcheck` catch unused exports.
   - *Dynamic*: Code coverage tools (e.g., `nyc`, Jest) can verify if code was actually executed in runtime.

2. **Safe and Gradual Removal**
   - **Feature flags**: Wrap sensitive logic to test changes before full deletion.
   - **Stepped pruning**: Start with files with zero imports and zero coverage, then escalate.
   - **CI checks**: Block commits if coverage drops or removed modules are still used.

3. **Testing and Verification**
   - **Snapshot testing**: Locks down UI/function outputs to catch regressions.
   - **Visual regression testing**: Use tools like Percy or Chromatic for UI verification.
   - **Small PRs**: Easier to review and revert in case of breakages.

---

## 4. Alternative Frameworks & Best Practices

**Why?** Additional tools can provide broader visibility or enforce coding discipline.

- **Madge**: Generates graphs of module dependencies, detects circular imports.
- **Snyk Code / CodeQL**: Also detect dead code while checking for security issues.
- **Useful ESLint Plugins**:
  - `eslint-plugin-unused-imports`: Auto-removes unused imports.
  - `@typescript-eslint/no-unused-vars`: Detects variables and function args that are never used.
- **Monorepo splitting**: Break large apps into packages. This enforces clean imports and reduces cross-contamination.

---

## 5. FAQ: Your Specific Questions

1. **What are accurate tools for dead code detection?**
   - Use `ts-prune`, `unimported`, `depcheck`, and bundle analyzers to cover both static and build-time visibility.

2. **How can I improve grep patterns for security modules?**
   ```bash
   rg -P "verifyAdminAccess\s*\(" -g "*.ts" -g "*.tsx"
````

- This ensures you're catching calls to `verifyAdminAccess`, regardless of spacing or formatting.

3. **How to automate import/export analysis?**

   - Combine AST tooling and dynamic code coverage.
   - Integrate ESLint rules and code removal scripts into your CI.
   - Use pre-commit hooks to prevent new unused code from being introduced.

4. **Should I use existing tools or write my own?**

   - Prefer existing, well-maintained tools unless you have highly specific needs. A combination of `ts-prune`, `Madge`, and `Bundle Analyzer` covers most use cases.

5. **How to safely verify before code removal?**

   - Set a baseline with code coverage tools.
   - Use flags to hide or disable logic temporarily.
   - Visually test components.
   - Only merge small, incremental removals with clear documentation.

---

*This document is designed to be used by AI agents or automation tools to help clean and optimize a complex TypeScript/JavaScript codebase efficiently and safely.*

```
```
